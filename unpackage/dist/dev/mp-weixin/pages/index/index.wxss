
.page.data-v-57280228 {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 140rpx;
}
.search-bar-fixed.data-v-57280228 {
	background: #fff;
	padding: 24rpx 32rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	gap: 20rpx;
	position: relative;
	z-index: 999;
	transition: all 0.3s ease;
}
.search-bar-sticky.data-v-57280228 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}
.search-input.data-v-57280228 {
	flex: 1;
}
.search-actions.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.recommend-section.data-v-57280228 {
	background: #fff;
	margin-top: 16rpx;
	display: flex;
	align-items: center;
}
.recommend-content.data-v-57280228 {
	padding: 24rpx 32rpx;
}
.recommend-tags.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex-wrap: wrap;
}
.func-bar.data-v-57280228 {
	background: #fff;
	padding: 40rpx 32rpx;
	margin-top: 16rpx;
	display: flex;
	justify-content: space-around;
}
.func-item.data-v-57280228 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}
.func-icon.data-v-57280228 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.func-text.data-v-57280228 {
	font-size: 24rpx;
	color: #666;
}
.sort-filter-bar.data-v-57280228 {
	background: #fff;
	padding: 24rpx 32rpx;
	margin-top: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #f0f0f0;
}
.sort-section.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 40rpx;
}
.sort-item.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 0;
	cursor: pointer;
	transition: all 0.3s ease;
}
.sort-item.active .sort-text.data-v-57280228 {
	color: #093C6B;
	font-weight: 600;
}
.sort-text.data-v-57280228 {
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s ease;
}
.filter-section.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 24rpx;
}
.filter-item.data-v-57280228 {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}
.filter-item.data-v-57280228:active {
	background: #e9ecef;
}
.filter-text.data-v-57280228 {
	font-size: 26rpx;
	color: #666;
}
.job-list.data-v-57280228 {
	padding: 16rpx 32rpx;
}
.job-card.data-v-57280228 {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.job-header.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 32rpx;
}
.job-info.data-v-57280228 {
	flex: 1;
}
.job-title.data-v-57280228 {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}
.job-salary.data-v-57280228 {
	font-size: 30rpx;
	color: #093C6B;
	font-weight: 600;
	margin-bottom: 16rpx;
	display: block;
}
.job-category.data-v-57280228 {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}
.job-location.data-v-57280228 {
	font-size: 28rpx;
	color: #666;
	display: block;
}
.job-footer.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.recruiter-info.data-v-57280228 {
	display: flex;
	align-items: center;
	flex: 1;
	gap: 20rpx;
}
.recruiter-details.data-v-57280228 {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}
.recruiter-name.data-v-57280228 {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}
.recruiter-company.data-v-57280228 {
	font-size: 24rpx;
	color: #666;
	max-width: 300rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.recruiter-activity.data-v-57280228 {
	font-size: 22rpx;
	color: #999;
}
.float-buttons.data-v-57280228 {
	position: fixed;
	bottom: 140rpx;
	left: 50%;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
	display: flex;
	z-index: 100;
	padding: 0 32rpx;
}

