<view class="page data-v-57280228"><view class="{{['search-bar-fixed','data-v-57280228',(isSearchSticky)?'search-bar-sticky':'']}}"><view class="search-input data-v-57280228"><u-search vue-id="8dd740cc-1" placeholder="焊工" show-action="{{false}}" bg-color="#f5f5f5" shape="round" value="{{searchKeyword}}" data-event-opts="{{[['^search',[['onSearch']]],['^input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l"></u-search></view><view class="search-actions data-v-57280228"><u-button vue-id="8dd740cc-2" type="primary" size="small" custom-style="{{({backgroundColor:'#4A90E2',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">搜索</u-button></view></view><view class="recommend-section data-v-57280228"><view class="recommend-header data-v-57280228"><text class="recommend-title data-v-57280228">推荐职位</text><view class="recommend-tags data-v-57280228"><block wx:for="{{selectedTags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><u-tag vue-id="{{'8dd740cc-3-'+__i0__}}" text="{{tag}}" size="mini" type="primary" plain="{{true}}" custom-style="{{({marginRight:'16rpx',fontSize:'24rpx'})}}" class="data-v-57280228" bind:__l="__l"></u-tag></block><u-icon vue-id="8dd740cc-4" name="plus" size="14" color="#093C6B" data-event-opts="{{[['^click',[['handleAddTag']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l"></u-icon></view></view></view><view class="func-bar data-v-57280228"><block wx:for="{{funcItems}}" wx:for-item="item" wx:for-index="__i1__" wx:key="name"><view data-event-opts="{{[['tap',[['handleFuncClick',['$0'],[[['funcItems','name',item.name]]]]]]]}}" class="func-item data-v-57280228" bindtap="__e"><view class="func-icon data-v-57280228"><u-icon vue-id="{{'8dd740cc-5-'+__i1__}}" name="{{item.icon}}" size="28" color="{{item.color}}" class="data-v-57280228" bind:__l="__l"></u-icon></view><text class="func-text data-v-57280228">{{item.name}}</text></view></block></view><view class="sort-filter-bar data-v-57280228"><view class="sort-section data-v-57280228"><block wx:for="{{$root.l0}}" wx:for-item="sort" wx:for-index="__i2__" wx:key="key"><view data-event-opts="{{[['tap',[['handleSortChange',['$0'],[[['sortOptions','key',sort.$orig.key,'key']]]]]]]}}" class="{{['sort-item','data-v-57280228',(currentSort===sort.$orig.key)?'active':'']}}" bindtap="__e"><text class="sort-text data-v-57280228">{{sort.$orig.label}}</text><block wx:if="{{sort.$orig.sortable}}"><u-icon vue-id="{{'8dd740cc-6-'+__i2__}}" name="{{sort.m0}}" size="12" color="{{currentSort===sort.$orig.key?'#093C6B':'#999'}}" class="data-v-57280228" bind:__l="__l"></u-icon></block></view></block></view><view class="filter-section data-v-57280228"><view data-event-opts="{{[['tap',[['handleLocationFilter',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><text class="filter-text data-v-57280228">{{currentLocation}}</text><u-icon vue-id="8dd740cc-7" name="arrow-down" size="12" color="#666" class="data-v-57280228" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['handleAdvancedFilter',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><u-icon vue-id="8dd740cc-8" name="list" size="16" color="#666" class="data-v-57280228" bind:__l="__l"></u-icon><text class="filter-text data-v-57280228">筛选</text></view></view></view><view class="job-list data-v-57280228"><block wx:for="{{$root.l1}}" wx:for-item="job" wx:for-index="__i3__" wx:key="id"><view class="job-card data-v-57280228"><view class="job-header data-v-57280228"><view class="job-info data-v-57280228"><text class="job-title data-v-57280228">{{job.$orig.title}}</text><text class="job-salary data-v-57280228">{{job.$orig.salary}}</text><text class="job-category data-v-57280228">{{job.$orig.category}}</text><text class="job-location data-v-57280228">{{job.$orig.location}}</text></view><u-icon vue-id="{{'8dd740cc-9-'+__i3__}}" name="close" size="16" color="#ccc" data-event-opts="{{[['^click',[['handleCloseJob',['$0'],[[['jobList','id',job.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l"></u-icon></view><view class="job-footer data-v-57280228"><view class="recruiter-info data-v-57280228"><u-avatar vue-id="{{'8dd740cc-10-'+__i3__}}" src="{{job.$orig.recruiter.avatar}}" size="40" class="data-v-57280228" bind:__l="__l"></u-avatar><view class="recruiter-details data-v-57280228"><text class="recruiter-name data-v-57280228">{{job.$orig.recruiter.name}}</text><text class="recruiter-company data-v-57280228">{{job.$orig.recruiter.company}}</text><text class="recruiter-activity data-v-57280228">{{job.$orig.recruiter.activity}}</text></view></view><u-button vue-id="{{'8dd740cc-11-'+__i3__}}" type="{{job.$orig.buttonText==='免费聊'?'primary':'info'}}" size="small" custom-style="{{job.a0}}" data-event-opts="{{[['^click',[['handleCommunicate',['$0'],[[['jobList','id',job.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">{{''+job.$orig.buttonText+''}}</u-button></view></view></block></view><view class="float-buttons data-v-57280228"><u-button vue-id="8dd740cc-12" type="primary" size="default" custom-style="{{({backgroundColor:'#093C6B',marginRight:'20rpx',borderRadius:'50rpx',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['handlePublishJob']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><u-icon vue-id="{{('8dd740cc-13')+','+('8dd740cc-12')}}" name="plus" color="white" size="16" class="data-v-57280228" bind:__l="__l"></u-icon>免费发布职位</u-button><u-button vue-id="8dd740cc-14" type="info" plain="{{true}}" size="default" custom-style="{{({borderRadius:'50rpx',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['handleManageResume']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">管理我的简历</u-button></view><custom-tabbar vue-id="8dd740cc-15" value="{{0}}" class="data-v-57280228" bind:__l="__l"></custom-tabbar></view>