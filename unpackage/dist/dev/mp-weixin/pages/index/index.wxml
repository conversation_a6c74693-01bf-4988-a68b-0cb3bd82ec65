<view class="page data-v-********"><view class="search-bar data-v-********"><view class="search-input data-v-********"><u-search vue-id="8dd740cc-1" placeholder="焊工" show-action="{{false}}" bg-color="#f5f5f5" shape="round" value="{{searchKeyword}}" data-event-opts="{{[['^search',[['onSearch']]],['^input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-********" bind:__l="__l"></u-search></view><view class="search-actions data-v-********"><u-button vue-id="8dd740cc-2" type="primary" size="small" custom-style="{{({backgroundColor:'#4A90E2',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l" vue-slots="{{['default']}}">搜索</u-button><view data-event-opts="{{[['tap',[['handleRecruit',['$event']]]]]}}" class="recruit-btn data-v-********" bindtap="__e"><u-icon vue-id="8dd740cc-3" name="account" size="16" color="#666" class="data-v-********" bind:__l="__l"></u-icon><text class="recruit-text data-v-********">我要招人</text></view></view></view><view class="recommend-section data-v-********"><view class="recommend-header data-v-********"><text class="recommend-title data-v-********">推荐职位</text><view class="recommend-tags data-v-********"><block wx:for="{{recommendTags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><u-tag vue-id="{{'8dd740cc-4-'+__i0__}}" text="{{tag}}" size="mini" type="primary" plain="{{true}}" custom-style="{{({marginRight:'16rpx',fontSize:'24rpx'})}}" class="data-v-********" bind:__l="__l"></u-tag></block><u-icon vue-id="8dd740cc-5" name="plus" size="14" color="#093C6B" class="data-v-********" bind:__l="__l"></u-icon></view></view></view><view class="func-bar data-v-********"><block wx:for="{{funcItems}}" wx:for-item="item" wx:for-index="__i1__" wx:key="name"><view data-event-opts="{{[['tap',[['handleFuncClick',['$0'],[[['funcItems','name',item.name]]]]]]]}}" class="func-item data-v-********" bindtap="__e"><view class="func-icon data-v-********"><u-icon vue-id="{{'8dd740cc-6-'+__i1__}}" name="{{item.icon}}" size="28" color="{{item.color}}" class="data-v-********" bind:__l="__l"></u-icon></view><text class="func-text data-v-********">{{item.name}}</text></view></block></view><view class="filter-bar data-v-********"><block wx:for="{{$root.l0}}" wx:for-item="filter" wx:for-index="index" wx:key="$orig"><u-tag vue-id="{{'8dd740cc-7-'+index}}" text="{{filter.$orig}}" size="small" type="{{filter.$orig==='深圳'?'primary':'info'}}" plain="{{filter.$orig!=='深圳'}}" custom-style="{{filter.a0}}" data-event-opts="{{[['^click',[['handleFilterClick',['$0'],[[['filters','',index]]]]]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l"></u-tag></block></view><view class="job-list data-v-********"><block wx:for="{{$root.l1}}" wx:for-item="job" wx:for-index="__i2__" wx:key="id"><view class="job-card data-v-********"><view class="job-header data-v-********"><view class="job-info data-v-********"><text class="job-title data-v-********">{{job.$orig.title}}</text><text class="job-salary data-v-********">{{job.$orig.salary}}</text><text class="job-category data-v-********">{{job.$orig.category}}</text><text class="job-location data-v-********">{{job.$orig.location}}</text></view><u-icon vue-id="{{'8dd740cc-8-'+__i2__}}" name="close" size="16" color="#ccc" data-event-opts="{{[['^click',[['handleCloseJob',['$0'],[[['jobList','id',job.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l"></u-icon></view><view class="job-footer data-v-********"><view class="recruiter-info data-v-********"><u-avatar vue-id="{{'8dd740cc-9-'+__i2__}}" src="{{job.$orig.recruiter.avatar}}" size="40" class="data-v-********" bind:__l="__l"></u-avatar><view class="recruiter-details data-v-********"><text class="recruiter-name data-v-********">{{job.$orig.recruiter.name}}</text><text class="recruiter-company data-v-********">{{job.$orig.recruiter.company}}</text><text class="recruiter-activity data-v-********">{{job.$orig.recruiter.activity}}</text></view></view><u-button vue-id="{{'8dd740cc-10-'+__i2__}}" type="{{job.$orig.buttonText==='免费聊'?'primary':'info'}}" size="small" custom-style="{{job.a1}}" data-event-opts="{{[['^click',[['handleCommunicate',['$0'],[[['jobList','id',job.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l" vue-slots="{{['default']}}">{{''+job.$orig.buttonText+''}}</u-button></view></view></block></view><view class="float-buttons data-v-********"><u-button vue-id="8dd740cc-11" type="primary" size="default" custom-style="{{({backgroundColor:'#093C6B',marginRight:'20rpx',borderRadius:'50rpx',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['handlePublishJob']]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l" vue-slots="{{['default']}}"><u-icon vue-id="{{('8dd740cc-12')+','+('8dd740cc-11')}}" name="plus" color="white" size="16" class="data-v-********" bind:__l="__l"></u-icon>免费发布职位</u-button><u-button vue-id="8dd740cc-13" type="info" plain="{{true}}" size="default" custom-style="{{({borderRadius:'50rpx',fontSize:'28rpx'})}}" data-event-opts="{{[['^click',[['handleManageResume']]]]}}" bind:click="__e" class="data-v-********" bind:__l="__l" vue-slots="{{['default']}}">管理我的简历</u-button></view><custom-tabbar vue-id="8dd740cc-14" value="{{0}}" class="data-v-********" bind:__l="__l"></custom-tabbar></view>