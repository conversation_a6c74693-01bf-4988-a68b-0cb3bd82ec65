{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?4db4", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?5a18", "uni-app:///main.js", null, "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?3a22", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?0686", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?399f", "uni-app:///pages/index/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabbar", "data", "searchKeyword", "isSearchSticky", "recommendTabs", "name", "currentRecommendTab", "funcItems", "icon", "color", "sortOptions", "key", "label", "sortable", "currentSort", "sortDirection", "currentLocation", "jobList", "id", "title", "salary", "category", "location", "recruiter", "company", "activity", "avatar", "buttonText", "computed", "currentRecommendTags", "mounted", "uni", "methods", "onSearch", "console", "handleRecommendTabChange", "handleAddTag", "handleFuncClick", "handleSortChange", "getSortIcon", "handleLocationFilter", "handleAdvancedFilter", "handleCommunicate", "handleCloseJob", "handlePublishJob", "handleManageResume"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyN;AACzN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCkIp4B;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACA;MACAC,gBACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC,YACA;QAAAF;QAAAG;QAAAC;MAAA,GACA;QAAAJ;QAAAG;QAAAC;MAAA,GACA;QAAAJ;QAAAG;QAAAC;MAAA,GACA;QAAAJ;QAAAG;QAAAC;MAAA,GACA;QAAAJ;QAAAG;QAAAC;MAAA,EACA;MACA;MACAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MAAA;MACAC;MACAC,UACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAlB;UACAmB;UACAC;UACAC;QACA;QACAC;MACA,GACA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAC;UACAlB;UACAmB;UACAC;UACAC;QACA;QACAC;MACA,GACA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAC;UACAlB;UACAmB;UACAC;UACAC;QACA;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACAD;IACA;IACAE;MACAF;MACA;IACA;IACAG;MACAH;IACA;IACAI;MAAA;MACA;QAAA;MAAA;QACA;QACA;MACA;QACA;QACA;MACA;MACAJ;IACA;IACAK;MAAA;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MACAN;MACA;IACA;IACAO;MACAP;MACA;IACA;IACAQ;MACAR;IACA;IACAS;MACAT;IACA;IACAU;MACAV;IACA;IACAW;MACAX;IACA;EACA;AACA;AAAA,2B", "file": "pages/index/index.js", "sourcesContent": ["import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753627611244\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabs/u-tabs\" */ \"uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-sticky/u-sticky\" */ \"uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-avatar/u-avatar\" */ \"uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.sortOptions, function (sort, __i1__) {\n    var $orig = _vm.__get_orig(sort)\n    var m0 = sort.sortable ? _vm.getSortIcon(sort.key) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var l1 = _vm.__map(_vm.jobList, function (job, __i2__) {\n    var $orig = _vm.__get_orig(job)\n    var a0 = {\n      backgroundColor: job.buttonText === \"免费聊\" ? \"#093C6B\" : \"#4A90E2\",\n      fontSize: \"26rpx\",\n      padding: \"12rpx 24rpx\",\n    }\n    return {\n      $orig: $orig,\n      a0: a0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- 固定搜索栏 -->\r\n\r\n\t\t\t<view class=\"search-bar-fixed\" :class=\"{ 'search-bar-sticky': isSearchSticky }\">\r\n\t\t\t\t<view class=\"search-input\">\r\n\t\t\t\t\t<u-search placeholder=\"焊工\" v-model=\"searchKeyword\" :show-action=\"false\" bg-color=\"#f5f5f5\"\r\n\t\t\t\t\t\tshape=\"round\" @search=\"onSearch\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-actions\">\r\n\t\t\t\t\t<u-button type=\"primary\" size=\"small\"\r\n\t\t\t\t\t\t:custom-style=\"{ backgroundColor: '#4A90E2', fontSize: '28rpx' }\" @click=\"onSearch\">\r\n\t\t\t\t\t\t搜索\r\n\t\t\t\t\t</u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<scroll-view>\r\n\t\t\t\t<view class=\"search-bar-fixed\">\r\n<!-- 推荐职位 Tabs -->\r\n<view class=\"recommend-section\">\r\n\t<view>\r\n\t\t<u-tabs :list=\"recommendTabs\" :current=\"currentRecommendTab\" @change=\"handleRecommendTabChange\"\r\n\t\t\t:active-color=\"'#093C6B'\" :inactive-color=\"'#666'\" font-size=\"28\" :bar-width=\"60\" bar-height=\"4\">\r\n\t\t</u-tabs>\r\n\t</view>\r\n\t<view class=\"recommend-content\">\r\n\t\t<view class=\"recommend-tags\">\r\n\t\t\t<u-icon name=\"plus\" size=\"14\" color=\"#093C6B\" @click=\"handleAddTag\" />\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n\r\n<!-- 功能图标 -->\r\n<view class=\"func-bar\">\r\n\t<view class=\"func-item\" v-for=\"item in funcItems\" :key=\"item.name\" @click=\"handleFuncClick(item)\">\r\n\t\t<view class=\"func-icon\">\r\n\t\t\t<u-icon :name=\"item.icon\" size=\"28\" :color=\"item.color\" />\r\n\t\t</view>\r\n\t\t<text class=\"func-text\">{{ item.name }}</text>\r\n\t</view>\r\n</view>\r\n<u-sticky>\r\n<!-- 排序筛选栏 -->\r\n<view class=\"sort-filter-bar\">\r\n\t<view class=\"sort-section\">\r\n\t\t<view class=\"sort-item\" v-for=\"sort in sortOptions\" :key=\"sort.key\"\r\n\t\t\t:class=\"{ 'active': currentSort === sort.key }\" @click=\"handleSortChange(sort.key)\">\r\n\t\t\t<text class=\"sort-text\">{{ sort.label }}</text>\r\n\t\t\t<u-icon v-if=\"sort.sortable\" :name=\"getSortIcon(sort.key)\" size=\"12\"\r\n\t\t\t\t:color=\"currentSort === sort.key ? '#093C6B' : '#999'\" />\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"filter-section\">\r\n\t\t<view class=\"filter-item\" @click=\"handleLocationFilter\">\r\n\t\t\t<text class=\"filter-text\">{{ currentLocation }}</text>\r\n\t\t\t<u-icon name=\"arrow-down\" size=\"12\" color=\"#666\" />\r\n\t\t</view>\r\n\t\t<view class=\"filter-item\" @click=\"handleAdvancedFilter\">\r\n\t\t\t<u-icon name=\"list\" size=\"16\" color=\"#666\" />\r\n\t\t\t<text class=\"filter-text\">筛选</text>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n</u-sticky>\r\n\r\n<!-- 职位列表 -->\r\n<view class=\"job-list\">\r\n\t<view class=\"job-card\" v-for=\"job in jobList\" :key=\"job.id\">\r\n\t\t<view class=\"job-header\">\r\n\t\t\t<view class=\"job-info\">\r\n\t\t\t\t<text class=\"job-title\">{{ job.title }}</text>\r\n\t\t\t\t<text class=\"job-salary\">{{ job.salary }}</text>\r\n\t\t\t\t<text class=\"job-category\">{{ job.category }}</text>\r\n\t\t\t\t<text class=\"job-location\">{{ job.location }}</text>\r\n\t\t\t</view>\r\n\t\t\t<u-icon name=\"close\" size=\"16\" color=\"#ccc\" @click=\"handleCloseJob(job)\" />\r\n\t\t</view>\r\n\r\n\t\t<view class=\"job-footer\">\r\n\t\t\t<view class=\"recruiter-info\">\r\n\t\t\t\t<u-avatar :src=\"job.recruiter.avatar\" size=\"40\" />\r\n\t\t\t\t<view class=\"recruiter-details\">\r\n\t\t\t\t\t<text class=\"recruiter-name\">{{ job.recruiter.name }}</text>\r\n\t\t\t\t\t<text class=\"recruiter-company\">{{ job.recruiter.company }}</text>\r\n\t\t\t\t\t<text class=\"recruiter-activity\">{{ job.recruiter.activity }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<u-button :type=\"job.buttonText === '免费聊' ? 'primary' : 'info'\" size=\"small\" :custom-style=\"{\r\n\t\t\t\tbackgroundColor: job.buttonText === '免费聊' ? '#093C6B' : '#4A90E2',\r\n\t\t\t\tfontSize: '26rpx',\r\n\t\t\t\tpadding: '12rpx 24rpx'\r\n\t\t\t}\" @click=\"handleCommunicate(job)\">\r\n\t\t\t\t{{ job.buttonText }}\r\n\t\t\t</u-button>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\r\n\t\t<!-- 底部浮动按钮 -->\r\n\t\t<view class=\"float-buttons\">\r\n\t\t\t<u-button type=\"primary\" size=\"default\" :custom-style=\"{\r\n\t\t\t\tbackgroundColor: '#093C6B',\r\n\t\t\t\tmarginRight: '20rpx',\r\n\t\t\t\tborderRadius: '50rpx',\r\n\t\t\t\tfontSize: '28rpx'\r\n\t\t\t}\" @click=\"handlePublishJob\">\r\n\t\t\t\t<u-icon name=\"plus\" color=\"white\" size=\"16\" />\r\n\t\t\t\t免费发布职位\r\n\t\t\t</u-button>\r\n\t\t\t<u-button type=\"info\" plain size=\"default\" :custom-style=\"{\r\n\t\t\t\tborderRadius: '50rpx',\r\n\t\t\t\tfontSize: '28rpx'\r\n\t\t\t}\" @click=\"handleManageResume\">\r\n\t\t\t\t管理我的简历\r\n\t\t\t</u-button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 公共TabBar -->\r\n\t\t<view>\r\n\t\t\t<custom-tabbar :value=\"0\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport CustomTabbar from '@/components/CustomTabbar.vue'\r\n\r\nexport default {\r\n\tcomponents: { CustomTabbar },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsearchKeyword: '',\r\n\t\t\tisSearchSticky: false,\r\n\t\t\t// 推荐职位 Tabs\r\n\t\t\trecommendTabs: [\r\n\t\t\t\t{ name: '推荐职位' },\r\n\t\t\t\t{ name: '前端开发工程师' },\r\n\t\t\t\t{ name: 'PHP' },\r\n\t\t\t\t{ name: '运营专员' },\r\n\t\t\t\t{ name: '测试工程师' },\r\n\t\t\t],\r\n\t\t\tcurrentRecommendTab: 0,\r\n\t\t\tfuncItems: [\r\n\t\t\t\t{ name: '免费进群', icon: 'account-fill', color: '#093C6B' },\r\n\t\t\t\t{ name: '名企招聘', icon: 'bookmark-fill', color: '#093C6B' },\r\n\t\t\t\t{ name: '人事行政', icon: 'file-text-fill', color: '#093C6B' },\r\n\t\t\t\t{ name: '客服运营', icon: 'server-man', color: '#093C6B' },\r\n\t\t\t\t{ name: '兼职专区', icon: 'clock-fill', color: '#093C6B' }\r\n\t\t\t],\r\n\t\t\t// 排序选项\r\n\t\t\tsortOptions: [\r\n\t\t\t\t{ key: 'comprehensive', label: '综合', sortable: false },\r\n\t\t\t\t{ key: 'latest', label: '最新', sortable: true },\r\n\t\t\t\t{ key: 'nearby', label: '附近', sortable: false }\r\n\t\t\t],\r\n\t\t\tcurrentSort: 'comprehensive',\r\n\t\t\tsortDirection: 'desc', // 'asc' 或 'desc'\r\n\t\t\tcurrentLocation: '深圳',\r\n\t\t\tjobList: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '前端开发工程师',\r\n\t\t\t\t\tsalary: '7000-12000元/月',\r\n\t\t\t\t\tcategory: '前端开发工程师',\r\n\t\t\t\t\tlocation: '宝安区',\r\n\t\t\t\t\trecruiter: {\r\n\t\t\t\t\t\tname: '杨先生',\r\n\t\t\t\t\t\tcompany: '深圳市恒鼎尚科技有限公司',\r\n\t\t\t\t\t\tactivity: '今日回复6次',\r\n\t\t\t\t\t\tavatar: '/static/avatar1.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbuttonText: '继续聊'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\ttitle: 'web前端开发工程师',\r\n\t\t\t\t\tsalary: '1-2万元/月',\r\n\t\t\t\t\tcategory: '前端开发工程师',\r\n\t\t\t\t\tlocation: '南山区',\r\n\t\t\t\t\trecruiter: {\r\n\t\t\t\t\t\tname: '杨先生',\r\n\t\t\t\t\t\tcompany: '胜道和科技有限公司',\r\n\t\t\t\t\t\tactivity: '今日回复7次',\r\n\t\t\t\t\t\tavatar: '/static/avatar2.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbuttonText: '继续聊'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\ttitle: 'Java后端开发',\r\n\t\t\t\t\tsalary: '10-20万元/月',\r\n\t\t\t\t\tcategory: 'Java',\r\n\t\t\t\t\tlocation: '龙岗区',\r\n\t\t\t\t\trecruiter: {\r\n\t\t\t\t\t\tname: '张先生',\r\n\t\t\t\t\t\tcompany: '星童云',\r\n\t\t\t\t\t\tactivity: '今日回复2次',\r\n\t\t\t\t\t\tavatar: '/static/avatar3.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbuttonText: '免费聊'\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tcurrentRecommendTags() {\r\n\t\t\treturn this.recommendTagsData[this.currentRecommendTab] || []\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// 监听页面滚动，实现搜索栏吸顶\r\n\t\tuni.onPageScroll((e) => {\r\n\t\t\tthis.isSearchSticky = e.scrollTop > 100\r\n\t\t})\r\n\t},\r\n\tmethods: {\r\n\t\tonSearch() {\r\n\t\t\tconsole.log('搜索:', this.searchKeyword)\r\n\t\t},\r\n\t\thandleRecommendTabChange(index) {\r\n\t\t\tthis.currentRecommendTab = index\r\n\t\t\tconsole.log('切换推荐标签:', this.recommendTabs[index].name)\r\n\t\t},\r\n\t\thandleAddTag() {\r\n\t\t\tconsole.log('添加标签')\r\n\t\t\t// 这里可以打开标签选择弹窗，让用户选择更多职位标签\r\n\t\t},\r\n\t\thandleFuncClick(item) {\r\n\t\t\tconsole.log('功能点击:', item.name)\r\n\t\t},\r\n\t\thandleSortChange(sortKey) {\r\n\t\t\tif (this.currentSort === sortKey && this.sortOptions.find(s => s.key === sortKey)?.sortable) {\r\n\t\t\t\t// 如果点击的是当前排序且可排序，则切换排序方向\r\n\t\t\t\tthis.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc'\r\n\t\t\t} else {\r\n\t\t\t\tthis.currentSort = sortKey\r\n\t\t\t\tthis.sortDirection = 'desc'\r\n\t\t\t}\r\n\t\t\tconsole.log('排序变更:', sortKey, this.sortDirection)\r\n\t\t},\r\n\t\tgetSortIcon(sortKey) {\r\n\t\t\tif (!this.sortOptions.find(s => s.key === sortKey)?.sortable) return ''\r\n\t\t\tif (this.currentSort !== sortKey) return 'arrow-down'\r\n\t\t\treturn this.sortDirection === 'asc' ? 'arrow-up' : 'arrow-down'\r\n\t\t},\r\n\t\thandleLocationFilter() {\r\n\t\t\tconsole.log('位置筛选')\r\n\t\t\t// 这里可以打开位置选择弹窗\r\n\t\t},\r\n\t\thandleAdvancedFilter() {\r\n\t\t\tconsole.log('高级筛选')\r\n\t\t\t// 这里可以打开筛选弹窗\r\n\t\t},\r\n\t\thandleCommunicate(job) {\r\n\t\t\tconsole.log('沟通职位:', job.title)\r\n\t\t},\r\n\t\thandleCloseJob(job) {\r\n\t\t\tconsole.log('关闭职位:', job.title)\r\n\t\t},\r\n\t\thandlePublishJob() {\r\n\t\t\tconsole.log('发布职位')\r\n\t\t},\r\n\t\thandleManageResume() {\r\n\t\t\tconsole.log('管理简历')\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page {\r\n\tbackground: #f8f9fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 140rpx;\r\n}\r\n\r\n.search-bar-fixed {\r\n\tbackground: #fff;\r\n\tpadding: 24rpx 32rpx;\r\n\tbox-sizing: border-box;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 999;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.search-bar-sticky {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-input {\r\n\tflex: 1;\r\n}\r\n\r\n.search-actions {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.recommend-section {\r\n\tbackground: #fff;\r\n\tmargin-top: 16rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.recommend-content {\r\n\tpadding: 24rpx 32rpx;\r\n}\r\n\r\n.recommend-tags {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tflex-wrap: wrap;\r\n}\r\n\r\n.func-bar {\r\n\tbackground: #fff;\r\n\tpadding: 40rpx 32rpx;\r\n\tmargin-top: 16rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n}\r\n\r\n.func-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 12rpx;\r\n}\r\n\r\n.func-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.func-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.sort-filter-bar {\r\n\tbackground: #fff;\r\n\tpadding: 24rpx 32rpx;\r\n\tmargin-top: 16rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.sort-section {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 40rpx;\r\n}\r\n\r\n.sort-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tpadding: 12rpx 0;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.sort-item.active .sort-text {\r\n\tcolor: #093C6B;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.sort-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.filter-section {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 24rpx;\r\n}\r\n\r\n.filter-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tpadding: 12rpx 16rpx;\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 20rpx;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.filter-item:active {\r\n\tbackground: #e9ecef;\r\n}\r\n\r\n.filter-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.job-list {\r\n\tpadding: 16rpx 32rpx;\r\n}\r\n\r\n.job-card {\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 32rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.job-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 32rpx;\r\n}\r\n\r\n.job-info {\r\n\tflex: 1;\r\n}\r\n\r\n.job-title {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 12rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.job-salary {\r\n\tfont-size: 30rpx;\r\n\tcolor: #093C6B;\r\n\tfont-weight: 600;\r\n\tmargin-bottom: 16rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.job-category {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.job-location {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n.job-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.recruiter-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.recruiter-details {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 6rpx;\r\n}\r\n\r\n.recruiter-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.recruiter-company {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmax-width: 300rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.recruiter-activity {\r\n\tfont-size: 22rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.float-buttons {\r\n\tposition: fixed;\r\n\tbottom: 140rpx;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\tdisplay: flex;\r\n\tz-index: 100;\r\n\tpadding: 0 32rpx;\r\n}\r\n</style>"], "sourceRoot": ""}