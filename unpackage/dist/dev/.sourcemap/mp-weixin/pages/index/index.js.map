{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?4db4", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?5a18", "uni-app:///main.js", null, "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?3a22", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?0686", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/pages/index/index.vue?399f", "uni-app:///pages/index/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabbar", "data", "searchKeyword", "recommendTags", "funcItems", "name", "icon", "color", "filters", "jobList", "id", "title", "salary", "category", "location", "recruiter", "company", "activity", "avatar", "buttonText", "methods", "onSearch", "console", "handleRecruit", "handleFuncClick", "handleFilterClick", "handleCommunicate", "handleCloseJob", "handlePublishJob", "handleManageResume"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyN;AACzN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,+QAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCwJp4B;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC,YACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC,UACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAV;UACAW;UACAC;UACAC;QACA;QACAC;MACA,GACA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAC;UACAV;UACAW;UACAC;UACAC;QACA;QACAC;MACA,GACA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAC;UACAV;UACAW;UACAC;UACAC;QACA;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACAE;MACAF;IACA;IACAG;MACAH;IACA;IACAI;MACAJ;IACA;IACAK;MACAL;IACA;IACAM;MACAN;IACA;IACAO;MACAP;IACA;EACA;AACA;AAAA,2B", "file": "pages/index/index.js", "sourcesContent": ["import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753625413110\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tag/u-tag\" */ \"uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-avatar/u-avatar\" */ \"uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filters, function (filter, index) {\n    var $orig = _vm.__get_orig(filter)\n    var a0 = {\n      marginRight: index < _vm.filters.length - 1 ? \"16rpx\" : \"0\",\n      fontSize: \"26rpx\",\n    }\n    return {\n      $orig: $orig,\n      a0: a0,\n    }\n  })\n  var l1 = _vm.__map(_vm.jobList, function (job, __i2__) {\n    var $orig = _vm.__get_orig(job)\n    var a1 = {\n      backgroundColor: job.buttonText === \"免费聊\" ? \"#093C6B\" : \"#4A90E2\",\n      fontSize: \"26rpx\",\n      padding: \"12rpx 24rpx\",\n    }\n    return {\n      $orig: $orig,\n      a1: a1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t  <!-- 搜索栏 -->\r\n\t  <view class=\"search-bar\">\r\n\t\t<view class=\"search-input\">\r\n\t\t  <u-search \r\n\t\t\tplaceholder=\"焊工\" \r\n\t\t\tv-model=\"searchKeyword\"\r\n\t\t\t:show-action=\"false\"\r\n\t\t\tbg-color=\"#f5f5f5\"\r\n\t\t\tshape=\"round\"\r\n\t\t\t@search=\"onSearch\"\r\n\t\t  />\r\n\t\t</view>\r\n\t\t<view class=\"search-actions\">\r\n\t\t  <u-button \r\n\t\t\ttype=\"primary\" \r\n\t\t\tsize=\"small\"\r\n\t\t\t:custom-style=\"{ backgroundColor: '#4A90E2', fontSize: '28rpx' }\"\r\n\t\t\t@click=\"onSearch\"\r\n\t\t  >\r\n\t\t\t搜索\r\n\t\t  </u-button>\r\n\t\t  <view class=\"recruit-btn\" @click=\"handleRecruit\">\r\n\t\t\t<u-icon name=\"account\" size=\"16\" color=\"#666\" />\r\n\t\t\t<text class=\"recruit-text\">我要招人</text>\r\n\t\t  </view>\r\n\t\t</view>\r\n\t  </view>\r\n  \r\n\t  <!-- 推荐职位 -->\r\n\t  <view class=\"recommend-section\">\r\n\t\t<view class=\"recommend-header\">\r\n\t\t  <text class=\"recommend-title\">推荐职位</text>\r\n\t\t  <view class=\"recommend-tags\">\r\n\t\t\t<u-tag \r\n\t\t\t  v-for=\"tag in recommendTags\" \r\n\t\t\t  :key=\"tag\"\r\n\t\t\t  :text=\"tag\"\r\n\t\t\t  size=\"mini\"\r\n\t\t\t  type=\"primary\"\r\n\t\t\t  :plain=\"true\"\r\n\t\t\t  :custom-style=\"{ marginRight: '16rpx', fontSize: '24rpx' }\"\r\n\t\t\t/>\r\n\t\t\t<u-icon name=\"plus\" size=\"14\" color=\"#093C6B\" />\r\n\t\t  </view>\r\n\t\t</view>\r\n\t  </view>\r\n  \r\n\t  <!-- 功能图标 -->\r\n\t  <view class=\"func-bar\">\r\n\t\t<view class=\"func-item\" v-for=\"item in funcItems\" :key=\"item.name\" @click=\"handleFuncClick(item)\">\r\n\t\t  <view class=\"func-icon\">\r\n\t\t\t<u-icon :name=\"item.icon\" size=\"28\" :color=\"item.color\" />\r\n\t\t  </view>\r\n\t\t  <text class=\"func-text\">{{ item.name }}</text>\r\n\t\t</view>\r\n\t  </view>\r\n  \r\n\t  <!-- 筛选条件 -->\r\n\t  <view class=\"filter-bar\">\r\n\t\t<u-tag \r\n\t\t  v-for=\"(filter, index) in filters\" \r\n\t\t  :key=\"filter\"\r\n\t\t  :text=\"filter\"\r\n\t\t  size=\"small\"\r\n\t\t  :type=\"filter === '深圳' ? 'primary' : 'info'\"\r\n\t\t  :plain=\"filter !== '深圳'\"\r\n\t\t  :custom-style=\"{ \r\n\t\t\tmarginRight: index < filters.length - 1 ? '16rpx' : '0',\r\n\t\t\tfontSize: '26rpx'\r\n\t\t  }\"\r\n\t\t  @click=\"handleFilterClick(filter)\"\r\n\t\t/>\r\n\t  </view>\r\n  \r\n\t  <!-- 职位列表 -->\r\n\t  <view class=\"job-list\">\r\n\t\t<view class=\"job-card\" v-for=\"job in jobList\" :key=\"job.id\">\r\n\t\t  <view class=\"job-header\">\r\n\t\t\t<view class=\"job-info\">\r\n\t\t\t  <text class=\"job-title\">{{ job.title }}</text>\r\n\t\t\t  <text class=\"job-salary\">{{ job.salary }}</text>\r\n\t\t\t  <text class=\"job-category\">{{ job.category }}</text>\r\n\t\t\t  <text class=\"job-location\">{{ job.location }}</text>\r\n\t\t\t</view>\r\n\t\t\t<u-icon name=\"close\" size=\"16\" color=\"#ccc\" @click=\"handleCloseJob(job)\" />\r\n\t\t  </view>\r\n\t\t  \r\n\t\t  <view class=\"job-footer\">\r\n\t\t\t<view class=\"recruiter-info\">\r\n\t\t\t  <u-avatar :src=\"job.recruiter.avatar\" size=\"40\" />\r\n\t\t\t  <view class=\"recruiter-details\">\r\n\t\t\t\t<text class=\"recruiter-name\">{{ job.recruiter.name }}</text>\r\n\t\t\t\t<text class=\"recruiter-company\">{{ job.recruiter.company }}</text>\r\n\t\t\t\t<text class=\"recruiter-activity\">{{ job.recruiter.activity }}</text>\r\n\t\t\t  </view>\r\n\t\t\t</view>\r\n\t\t\t<u-button \r\n\t\t\t  :type=\"job.buttonText === '免费聊' ? 'primary' : 'info'\"\r\n\t\t\t  size=\"small\"\r\n\t\t\t  :custom-style=\"{ \r\n\t\t\t\tbackgroundColor: job.buttonText === '免费聊' ? '#093C6B' : '#4A90E2',\r\n\t\t\t\tfontSize: '26rpx',\r\n\t\t\t\tpadding: '12rpx 24rpx'\r\n\t\t\t  }\"\r\n\t\t\t  @click=\"handleCommunicate(job)\"\r\n\t\t\t>\r\n\t\t\t  {{ job.buttonText }}\r\n\t\t\t</u-button>\r\n\t\t  </view>\r\n\t\t</view>\r\n\t  </view>\r\n  \r\n\t  <!-- 底部浮动按钮 -->\r\n\t  <view class=\"float-buttons\">\r\n\t\t<u-button \r\n\t\t  type=\"primary\"\r\n\t\t  size=\"default\"\r\n\t\t  :custom-style=\"{ \r\n\t\t\tbackgroundColor: '#093C6B', \r\n\t\t\tmarginRight: '20rpx',\r\n\t\t\tborderRadius: '50rpx',\r\n\t\t\tfontSize: '28rpx'\r\n\t\t  }\"\r\n\t\t  @click=\"handlePublishJob\"\r\n\t\t>\r\n\t\t  <u-icon name=\"plus\" color=\"white\" size=\"16\" />\r\n\t\t  免费发布职位\r\n\t\t</u-button>\r\n\t\t<u-button \r\n\t\t  type=\"info\"\r\n\t\t  plain\r\n\t\t  size=\"default\"\r\n\t\t  :custom-style=\"{ \r\n\t\t\tborderRadius: '50rpx',\r\n\t\t\tfontSize: '28rpx'\r\n\t\t  }\"\r\n\t\t  @click=\"handleManageResume\"\r\n\t\t>\r\n\t\t  管理我的简历\r\n\t\t</u-button>\r\n\t  </view>\r\n  \r\n\t  <!-- 公共TabBar -->\r\n\t  <custom-tabbar :value=\"0\" />\r\n\t</view>\r\n  </template>\r\n  \r\n  <script>\r\n  import CustomTabbar from '@/components/CustomTabbar.vue'\r\n  \r\n  export default {\r\n\tcomponents: { CustomTabbar },\r\n\tdata() {\r\n\t  return {\r\n\t\tsearchKeyword: '',\r\n\t\trecommendTags: ['前端开发工程师', 'PHP'],\r\n\t\tfuncItems: [\r\n\t\t  { name: '免费进群', icon: 'account-fill', color: '#093C6B' },\r\n\t\t  { name: '名企招聘', icon: 'bookmark-fill', color: '#093C6B' },\r\n\t\t  { name: '人事行政', icon: 'file-text-fill', color: '#093C6B' },\r\n\t\t  { name: '客服运营', icon: 'customer-service-fill', color: '#093C6B' },\r\n\t\t  { name: '兼职专区', icon: 'clock-fill', color: '#093C6B' }\r\n\t\t],\r\n\t\tfilters: ['综合', '最新', '附近', '深圳', '筛选'],\r\n\t\tjobList: [\r\n\t\t  {\r\n\t\t\tid: 1,\r\n\t\t\ttitle: '前端开发工程师',\r\n\t\t\tsalary: '7000-12000元/月',\r\n\t\t\tcategory: '前端开发工程师',\r\n\t\t\tlocation: '宝安区',\r\n\t\t\trecruiter: {\r\n\t\t\t  name: '杨先生',\r\n\t\t\t  company: '深圳市恒鼎尚科技有限公司',\r\n\t\t\t  activity: '今日回复6次',\r\n\t\t\t  avatar: '/static/avatar1.png'\r\n\t\t\t},\r\n\t\t\tbuttonText: '继续聊'\r\n\t\t  },\r\n\t\t  {\r\n\t\t\tid: 2,\r\n\t\t\ttitle: 'web前端开发工程师',\r\n\t\t\tsalary: '1-2万元/月',\r\n\t\t\tcategory: '前端开发工程师',\r\n\t\t\tlocation: '南山区',\r\n\t\t\trecruiter: {\r\n\t\t\t  name: '杨先生',\r\n\t\t\t  company: '胜道和科技有限公司',\r\n\t\t\t  activity: '今日回复7次',\r\n\t\t\t  avatar: '/static/avatar2.png'\r\n\t\t\t},\r\n\t\t\tbuttonText: '继续聊'\r\n\t\t  },\r\n\t\t  {\r\n\t\t\tid: 3,\r\n\t\t\ttitle: 'Java后端开发',\r\n\t\t\tsalary: '10-20万元/月',\r\n\t\t\tcategory: 'Java',\r\n\t\t\tlocation: '龙岗区',\r\n\t\t\trecruiter: {\r\n\t\t\t  name: '张先生',\r\n\t\t\t  company: '星童云',\r\n\t\t\t  activity: '今日回复2次',\r\n\t\t\t  avatar: '/static/avatar3.png'\r\n\t\t\t},\r\n\t\t\tbuttonText: '免费聊'\r\n\t\t  }\r\n\t\t]\r\n\t  }\r\n\t},\r\n\tmethods: {\r\n\t  onSearch() {\r\n\t\tconsole.log('搜索:', this.searchKeyword)\r\n\t  },\r\n\t  handleRecruit() {\r\n\t\tconsole.log('我要招人')\r\n\t  },\r\n\t  handleFuncClick(item) {\r\n\t\tconsole.log('功能点击:', item.name)\r\n\t  },\r\n\t  handleFilterClick(filter) {\r\n\t\tconsole.log('筛选:', filter)\r\n\t  },\r\n\t  handleCommunicate(job) {\r\n\t\tconsole.log('沟通职位:', job.title)\r\n\t  },\r\n\t  handleCloseJob(job) {\r\n\t\tconsole.log('关闭职位:', job.title)\r\n\t  },\r\n\t  handlePublishJob() {\r\n\t\tconsole.log('发布职位')\r\n\t  },\r\n\t  handleManageResume() {\r\n\t\tconsole.log('管理简历')\r\n\t  }\r\n\t}\r\n  }\r\n  </script>\r\n  \r\n  <style scoped>\r\n  .page {\r\n\tbackground: #f8f9fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 140rpx;\r\n  }\r\n  \r\n  .search-bar {\r\n\tbackground: #fff;\r\n\tpadding: 24rpx 32rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n  }\r\n  \r\n  .search-input {\r\n\tflex: 1;\r\n  }\r\n  \r\n  .search-actions {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n  }\r\n  \r\n  .recruit-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n  }\r\n  \r\n  .recruit-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n  }\r\n  \r\n  .recommend-section {\r\n\tbackground: #fff;\r\n\tpadding: 32rpx;\r\n\tmargin-top: 16rpx;\r\n  }\r\n  \r\n  .recommend-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n  }\r\n  \r\n  .recommend-title {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n  }\r\n  \r\n  .recommend-tags {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n  }\r\n  \r\n  .func-bar {\r\n\tbackground: #fff;\r\n\tpadding: 40rpx 32rpx;\r\n\tmargin-top: 16rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n  }\r\n  \r\n  .func-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 12rpx;\r\n  }\r\n  \r\n  .func-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n  }\r\n  \r\n  .func-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n  }\r\n  \r\n  .filter-bar {\r\n\tbackground: #fff;\r\n\tpadding: 24rpx 32rpx;\r\n\tmargin-top: 16rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n  }\r\n  \r\n  .job-list {\r\n\tpadding: 16rpx 32rpx;\r\n  }\r\n  \r\n  .job-card {\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 32rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);\r\n  }\r\n  \r\n  .job-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 32rpx;\r\n  }\r\n  \r\n  .job-info {\r\n\tflex: 1;\r\n  }\r\n  \r\n  .job-title {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 12rpx;\r\n\tdisplay: block;\r\n  }\r\n  \r\n  .job-salary {\r\n\tfont-size: 30rpx;\r\n\tcolor: #093C6B;\r\n\tfont-weight: 600;\r\n\tmargin-bottom: 16rpx;\r\n\tdisplay: block;\r\n  }\r\n  \r\n  .job-category {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n  }\r\n  \r\n  .job-location {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n  }\r\n  \r\n  .job-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n  }\r\n  \r\n  .recruiter-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tgap: 20rpx;\r\n  }\r\n  \r\n  .recruiter-details {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 6rpx;\r\n  }\r\n  \r\n  .recruiter-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n  }\r\n  \r\n  .recruiter-company {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmax-width: 300rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n  }\r\n  \r\n  .recruiter-activity {\r\n\tfont-size: 22rpx;\r\n\tcolor: #999;\r\n  }\r\n  \r\n  .float-buttons {\r\n\tposition: fixed;\r\n\tbottom: 140rpx;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\tdisplay: flex;\r\n\tz-index: 100;\r\n\tpadding: 0 32rpx;\r\n  }\r\n  </style>"], "sourceRoot": ""}