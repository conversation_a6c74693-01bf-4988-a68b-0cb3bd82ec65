{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?2644", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?563b", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?c0c3", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?d459", "uni-app:///node_modules/uview-ui/components/u-sticky/u-sticky.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?44ff", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-sticky/u-sticky.vue?4c8b"], "names": ["name", "mixins", "data", "cssSticky", "stickyTop", "elId", "left", "width", "height", "fixed", "computed", "style", "sticky<PERSON>ontent", "uZindex", "mounted", "methods", "init", "initObserveContent", "<PERSON><PERSON><PERSON><PERSON>", "thresholds", "contentObserver", "top", "setFixed", "disconnectObserver", "observer", "getStickyTop", "checkSupportCssSticky", "checkComputedStyle", "uni", "computedStyle", "resolve", "checkCssStickyForH5", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+N;AAC/N,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAi5B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgBr6B;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAeA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;QACA;MACA;QACA;;QAKAA;MAEA;MACAA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAD;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACAE;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACAC;MACA;MACA;MACAC;QACAC;MACA;MACA;MACAD;QACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAQA;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAGA;gBACA;kBACA;gBACA;;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;IACAC;MAAA;MACA;;MAEA;QACAC;UACAC;QACA;UACAC;QACA;MACA;IAEA;IACA;IACA;IACAC;MACA;IAAA;EAcA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzMA;AAAA;AAAA;AAAA;AAAosD,CAAgB,67CAAG,EAAC,C;;;;;;;;;;;ACAxtD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-sticky/u-sticky.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-sticky.vue?vue&type=template&id=c187ecf2&scoped=true&\"\nvar renderjs\nimport script from \"./u-sticky.vue?vue&type=script&lang=js&\"\nexport * from \"./u-sticky.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-sticky.vue?vue&type=style&index=0&id=c187ecf2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c187ecf2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-sticky/u-sticky.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=template&id=c187ecf2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n  var s1 = _vm.__get_style([_vm.stickyContent])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-sticky\"\n\t\t:id=\"elId\"\n\t\t:style=\"[style]\"\n\t>\n\t\t<view\n\t\t\t:style=\"[stickyContent]\"\n\t\t\tclass=\"u-sticky__content\"\n\t\t>\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';;\n\t/**\n\t * sticky 吸顶\n\t * @description 该组件与CSS中position: sticky属性实现的效果一致，当组件达到预设的到顶部距离时， 就会固定在指定位置，组件位置大于预设的顶部距离时，会重新按照正常的布局排列。\n\t * @tutorial https://www.uviewui.com/components/sticky.html\n\t * @property {String ｜ Number}\toffsetTop\t\t吸顶时与顶部的距离，单位px（默认 0 ）\n\t * @property {String ｜ Number}\tcustomNavHeight\t自定义导航栏的高度 （h5 默认44  其他默认 0 ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否开启吸顶功能 （默认 false ）\n\t * @property {String}\t\t\tbgColor\t\t\t组件背景颜色（默认 '#ffffff' ）\n\t * @property {String ｜ Number}\tzIndex\t\t\t吸顶时的z-index值\n\t * @property {String ｜ Number}\tindex\t\t\t自定义标识，用于区分是哪一个组件\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * @event {Function} fixed\t\t组件吸顶时触发\n\t * @event {Function} unfixed\t组件取消吸顶时触发\n\t * @example <u-sticky offsetTop=\"200\"><view>塞下秋来风景异，衡阳雁去无留意</view></u-sticky>\n\t */\n\texport default {\n\t\tname: 'u-sticky',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcssSticky: false, // 是否使用css的sticky实现\n\t\t\t\tstickyTop: 0, // 吸顶的top值，因为可能受自定义导航栏影响，最终的吸顶值非offsetTop值\n\t\t\t\telId: uni.$u.guid(),\n\t\t\t\tleft: 0, // js模式时，吸顶的内容因为处于postition: fixed模式，为了和原来保持一致的样式，需要记录并重新设置它的left，height，width属性\n\t\t\t\twidth: 'auto',\n\t\t\t\theight: 'auto',\n\t\t\t\tfixed: false, // js模式时，是否处于吸顶模式\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tstyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tif(!this.disabled) {\n\t\t\t\t\tif (this.cssSticky) {\n\t\t\t\t\t\tstyle.position = 'sticky'\n\t\t\t\t\t\tstyle.zIndex = this.uZindex\n\t\t\t\t\t\tstyle.top = uni.$u.addUnit(this.stickyTop)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstyle.height = this.fixed ? this.height + 'px' : 'auto'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 无需吸顶时，设置会默认的relative(nvue)和非nvue的static静态模式即可\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tstyle.position = 'relative'\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tstyle.position = 'static'\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\treturn uni.$u.deepMerge(uni.$u.addStyle(this.customStyle), style)\n\t\t\t},\n\t\t\t// 吸顶内容的样式\n\t\t\tstickyContent() {\n\t\t\t\tconst style = {}\n\t\t\t\tif (!this.cssSticky) {\n\t\t\t\t\tstyle.position = this.fixed ? 'fixed' : 'static'\n\t\t\t\t\tstyle.top = this.stickyTop + 'px'\n\t\t\t\t\tstyle.left = this.left + 'px'\n\t\t\t\t\tstyle.width = this.width == 'auto' ? 'auto' : this.width + 'px'\n\t\t\t\t\tstyle.zIndex = this.uZindex\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tuZindex() {\n\t\t\t\treturn this.zIndex ? this.zIndex : uni.$u.zIndex.sticky\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tthis.getStickyTop()\n\t\t\t\t// 判断使用的模式\n\t\t\t\tthis.checkSupportCssSticky()\n\t\t\t\t// 如果不支持css sticky，则使用js方案，此方案性能比不上css方案\n\t\t\t\tif (!this.cssSticky) {\n\t\t\t\t\t!this.disabled && this.initObserveContent()\n\t\t\t\t}\n\t\t\t},\n\t\t\tinitObserveContent() {\n\t\t\t\t// 获取吸顶内容的高度，用于在js吸顶模式时，给父元素一个填充高度，防止\"塌陷\"\n\t\t\t\tthis.$uGetRect('#' + this.elId).then((res) => {\n\t\t\t\t\tthis.height = res.height\n\t\t\t\t\tthis.left = res.left\n\t\t\t\t\tthis.width = res.width\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.observeContent()\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\tobserveContent() {\n\t\t\t\t// 先断掉之前的观察\n\t\t\t\tthis.disconnectObserver('contentObserver')\n\t\t\t\tconst contentObserver = uni.createIntersectionObserver({\n\t\t\t\t\t// 检测的区间范围\n\t\t\t\t\tthresholds: [0.95, 0.98, 1]\n\t\t\t\t})\n\t\t\t\t// 到屏幕顶部的高度时触发\n\t\t\t\tcontentObserver.relativeToViewport({\n\t\t\t\t\ttop: -this.stickyTop\n\t\t\t\t})\n\t\t\t\t// 绑定观察的元素\n\t\t\t\tcontentObserver.observe(`#${this.elId}`, res => {\n\t\t\t\t\tthis.setFixed(res.boundingClientRect.top)\n\t\t\t\t})\n\t\t\t\tthis.contentObserver = contentObserver\n\t\t\t},\n\t\t\tsetFixed(top) {\n\t\t\t\t// 判断是否出于吸顶条件范围\n\t\t\t\tconst fixed = top <= this.stickyTop\n\t\t\t\tthis.fixed = fixed\n\t\t\t},\n\t\t\tdisconnectObserver(observerName) {\n\t\t\t\t// 断掉观察，释放资源\n\t\t\t\tconst observer = this[observerName]\n\t\t\t\tobserver && observer.disconnect()\n\t\t\t},\n\t\t\tgetStickyTop() {\n\t\t\t\tthis.stickyTop = uni.$u.getPx(this.offsetTop) + uni.$u.getPx(this.customNavHeight)\n\t\t\t},\n\t\t\tasync checkSupportCssSticky() {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// H5，一般都是现代浏览器，是支持css sticky的，这里使用创建元素嗅探的形式判断\n\t\t\t\tif (this.checkCssStickyForH5()) {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\t// 如果安卓版本高于8.0，依然认为是支持css sticky的(因为安卓7在某些机型，可能不支持sticky)\n\t\t\t\tif (uni.$u.os() === 'android' && Number(uni.$u.sys().system) > 8) {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\n\t\t\t\t// APP-Vue和微信平台，通过computedStyle判断是否支持css sticky\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN\n\t\t\t\tthis.cssSticky = await this.checkComputedStyle()\n\t\t\t\t// #endif\n\n\t\t\t\t// ios上，从ios6开始，都是支持css sticky的\n\t\t\t\tif (uni.$u.os() === 'ios') {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\n\t\t\t\t// nvue，是支持css sticky的\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.cssSticky = true\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 在APP和微信小程序上，通过uni.createSelectorQuery可以判断是否支持css sticky\n\t\t\tcheckComputedStyle() {\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tuni.createSelectorQuery().in(this).select('.u-sticky').fields({\n\t\t\t\t\t\tcomputedStyle: [\"position\"]\n\t\t\t\t\t}).exec(e => {\n\t\t\t\t\t\tresolve('sticky' === e[0].position)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// H5通过创建元素的形式嗅探是否支持css sticky\n\t\t\t// 判断浏览器是否支持sticky属性\n\t\t\tcheckCssStickyForH5() {\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\n\t\t\t\t// #ifdef H5\n\t\t\t\tconst vendorList = ['', '-webkit-', '-ms-', '-moz-', '-o-'],\n\t\t\t\t\tvendorListLength = vendorList.length,\n\t\t\t\t\tstickyElement = document.createElement('div')\n\t\t\t\tfor (let i = 0; i < vendorListLength; i++) {\n\t\t\t\t\tstickyElement.style.position = vendorList[i] + 'sticky'\n\t\t\t\t\tif (stickyElement.style.position !== '') {\n\t\t\t\t\t\treturn true\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\tthis.disconnectObserver('contentObserver')\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.u-sticky {\n\t\t/* #ifdef APP-VUE || MP-WEIXIN */\n\t\t// 此处默认写sticky属性，是为了给微信和APP通过uni.createSelectorQuery查询是否支持css sticky使用\n\t\tposition: sticky;\n\t\t/* #endif */\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=style&index=0&id=c187ecf2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-sticky.vue?vue&type=style&index=0&id=c187ecf2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753627989385\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}