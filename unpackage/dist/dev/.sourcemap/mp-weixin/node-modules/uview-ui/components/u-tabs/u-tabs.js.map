{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?58bf", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?4f27", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?999c", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?d946", "uni-app:///node_modules/uview-ui/components/u-tabs/u-tabs.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?be6a", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabs/u-tabs.vue?e3bc"], "names": ["name", "mixins", "data", "firstTime", "scrollLeft", "scrollViewWidth", "lineOffsetLeft", "tabsRect", "left", "innerCurrent", "moving", "watch", "current", "immediate", "handler", "list", "computed", "textStyle", "addStyle", "style", "propsBadge", "mounted", "methods", "setLineLeft", "slice", "reduce", "setTimeout", "animation", "clickHandler", "item", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "uni", "setScrollLeft", "right", "resize", "Promise", "itemRect", "getTabsRect", "getAllItemRect", "queryRect", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+N;AAC/N,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAA+4B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuFn6B;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QAAA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;QACA,gGACAC,SACA;QACA;QACA;UACAC;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;MACA,+BACAC,4BACAC;QAAA;MAAA;MACA;MACA;MACA;;MAMA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;IAUA;IACA;IACAC;MACA;MACA,oDACAC;QACAC;MAAA,GACA;MACA;MACA;MACA;MACA;MACA,qDACAD;QACAC;MAAA,GACA;IACA;IACA;IACAC;MACA,wDACAF;QACAC;MAAA,GACA;IACA;IACAE;MAAA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA,2BACAV,4BACAC;QACA;MACA;MACA;MACA;MACA;MACA,4GACAU;MACA;MACA/B;MACA;IACA;IACA;IACAgC;MAAA;MACA;MACA;QACA;MACA;MACAC;QAAA;UAAA9B;UAAA;UAAA+B;QACA;QACA;QACAA;UACA;UACA;UACA;UACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UAAA;QAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UAAA,6DACAV;QAAA;QACAO;UAAA;QAAA;MACA;IACA;IACA;IACAI;MAAA;MAEA;MACA;MACA;QACA;UACAC;QACA;MACA;IAYA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3SA;AAAA;AAAA;AAAA;AAAksD,CAAgB,27CAAG,EAAC,C;;;;;;;;;;;ACAttD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-tabs/u-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabs.vue?vue&type=template&id=0de61367&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabs.vue?vue&type=style&index=0&id=0de61367&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0de61367\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-tabs/u-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=template&id=0de61367&scoped=true&\"", "var components\ntry {\n  components = {\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-badge/u-badge\" */ \"uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    _vm.$u.addStyle(_vm.itemStyle),\n    {\n      flex: _vm.scrollable ? \"\" : 1,\n    },\n  ])\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s1 = _vm.__get_style([_vm.textStyle(index)])\n    return {\n      $orig: $orig,\n      s1: s1,\n    }\n  })\n  var g0 = _vm.$u.addUnit(_vm.lineWidth)\n  var g1 = _vm.$u.addUnit(_vm.lineHeight)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-tabs\">\n\t\t<view class=\"u-tabs__wrapper\">\n\t\t\t<slot name=\"left\" />\n\t\t\t<view class=\"u-tabs__wrapper__scroll-view-wrapper\">\n\t\t\t\t<scroll-view\n\t\t\t\t\t:scroll-x=\"scrollable\"\n\t\t\t\t\t:scroll-left=\"scrollLeft\"\n\t\t\t\t\tscroll-with-animation\n\t\t\t\t\tclass=\"u-tabs__wrapper__scroll-view\"\n\t\t\t\t\t:show-scrollbar=\"false\"\n\t\t\t\t\tref=\"u-tabs__wrapper__scroll-view\"\n\t\t\t\t>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav\"\n\t\t\t\t\t\tref=\"u-tabs__wrapper__nav\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item\"\n\t\t\t\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t@tap=\"clickHandler(item, index)\"\n\t\t\t\t\t\t\t@longpress=\"longPressHandler(item,index)\"\n\t\t\t\t\t\t\t:ref=\"`u-tabs__wrapper__nav__item-${index}`\"\n\t\t\t\t\t\t\t:style=\"[$u.addStyle(itemStyle), {flex: scrollable ? '' : 1}]\"\n\t\t\t\t\t\t\t:class=\"[`u-tabs__wrapper__nav__item-${index}`, item.disabled && 'u-tabs__wrapper__nav__item--disabled']\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text\n\t\t\t\t\t\t\t\t:class=\"[item.disabled && 'u-tabs__wrapper__nav__item__text--disabled']\"\n\t\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item__text\"\n\t\t\t\t\t\t\t\t:style=\"[textStyle(index)]\"\n\t\t\t\t\t\t\t>{{ item[keyName] }}</text>\n\t\t\t\t\t\t\t<u-badge\n\t\t\t\t\t\t\t\t:show=\"!!(item.badge && (item.badge.show || item.badge.isDot || item.badge.value))\"\n\t\t\t\t\t\t\t\t:isDot=\"item.badge && item.badge.isDot || propsBadge.isDot\"\n\t\t\t\t\t\t\t\t:value=\"item.badge && item.badge.value || propsBadge.value\"\n\t\t\t\t\t\t\t\t:max=\"item.badge && item.badge.max || propsBadge.max\"\n\t\t\t\t\t\t\t\t:type=\"item.badge && item.badge.type || propsBadge.type\"\n\t\t\t\t\t\t\t\t:showZero=\"item.badge && item.badge.showZero || propsBadge.showZero\"\n\t\t\t\t\t\t\t\t:bgColor=\"item.badge && item.badge.bgColor || propsBadge.bgColor\"\n\t\t\t\t\t\t\t\t:color=\"item.badge && item.badge.color || propsBadge.color\"\n\t\t\t\t\t\t\t\t:shape=\"item.badge && item.badge.shape || propsBadge.shape\"\n\t\t\t\t\t\t\t\t:numberType=\"item.badge && item.badge.numberType || propsBadge.numberType\"\n\t\t\t\t\t\t\t\t:inverted=\"item.badge && item.badge.inverted || propsBadge.inverted\"\n\t\t\t\t\t\t\t\tcustomStyle=\"margin-left: 4px;\"\n\t\t\t\t\t\t\t></u-badge>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\t\t\twidth: $u.addUnit(lineWidth),\n\t\t\t\t\t\t\t\t\theight: $u.addUnit(lineHeight),\n\t\t\t\t\t\t\t\t\tbackground: lineColor,\n\t\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\n\t\t\t\t\t\t\t\t}]\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\t\t\t\twidth: $u.addUnit(lineWidth),\n\t\t\t\t\t\t\t\t\t\ttransform: `translate(${lineOffsetLeft}px)`,\n\t\t\t\t\t\t\t\t\t\ttransitionDuration: `${firstTime ? 0 : duration}ms`,\n\t\t\t\t\t\t\t\t\t\theight: $u.addUnit(lineHeight),\n\t\t\t\t\t\t\t\t\t\tbackground: lineColor,\n\t\t\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\n\t\t\t\t\t\t\t\t\t}]\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t<slot name=\"right\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\tconst animation = uni.requireNativePlugin('animation')\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\timport props from './props.js';\n\t/**\n\t * Tabs 标签\n\t * @description tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\n\t * @tutorial https://www.uviewui.com/components/tabs.html\n\t * @property {String | Number}\tduration\t\t\t滑块移动一次所需的时间，单位秒（默认 200 ）\n\t * @property {String | Number}\tswierWidth\t\t\tswiper的宽度（默认 '750rpx' ）\n\t * @property {String}\tkeyName\t 从`list`元素对象中读取的键名（默认 'name' ）\n\t * @event {Function(index)} change 标签改变时触发 index: 点击了第几个tab，索引从0开始\n\t * @event {Function(index)} click 点击标签时触发 index: 点击了第几个tab，索引从0开始\n  \t * @event {Function(index)} longPress 长按标签时触发 index: 点击了第几个tab，索引从0开始\n\t * @example <u-tabs :list=\"list\" :is-scroll=\"false\" :current=\"current\" @change=\"change\" @longPress=\"longPress\"></u-tabs>\n\t */\n\texport default {\n\t\tname: 'u-tabs',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfirstTime: true,\n\t\t\t\tscrollLeft: 0,\n\t\t\t\tscrollViewWidth: 0,\n\t\t\t\tlineOffsetLeft: 0,\n\t\t\t\ttabsRect: {\n\t\t\t\t\tleft: 0\n\t\t\t\t},\n\t\t\t\tinnerCurrent: 0,\n\t\t\t\tmoving: false,\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcurrent: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler (newValue, oldValue) {\n\t\t\t\t\t// 内外部值不相等时，才尝试移动滑块\n\t\t\t\t\tif (newValue !== this.innerCurrent) {\n\t\t\t\t\t\tthis.innerCurrent = newValue\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.resize()\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// list变化时，重新渲染list各项信息\n\t\t\tlist() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.resize()\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ttextStyle() {\n\t\t\t\treturn index => {\n\t\t\t\t\tconst style = {}\n\t\t\t\t\t// 取当期是否激活的样式\n\t\t\t\t\tconst customeStyle = index === this.innerCurrent ? uni.$u.addStyle(this.activeStyle) : uni.$u\n\t\t\t\t\t\t.addStyle(\n\t\t\t\t\t\t\tthis.inactiveStyle)\n\t\t\t\t\t// 如果当前菜单被禁用，则加上对应颜色，需要在此做处理，是因为nvue下，无法在style样式中通过!import覆盖标签的内联样式\n\t\t\t\t\tif (this.list[index].disabled) {\n\t\t\t\t\t\tstyle.color = '#c8c9cc'\n\t\t\t\t\t}\n\t\t\t\t\treturn uni.$u.deepMerge(customeStyle, style)\n\t\t\t\t}\n\t\t\t},\n\t\t\tpropsBadge() {\n\t\t\t\treturn uni.$u.props.badge\n\t\t\t}\n\t\t},\n\t\tasync mounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tsetLineLeft() {\n\t\t\t\tconst tabItem = this.list[this.innerCurrent];\n\t\t\t\tif (!tabItem) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 获取滑块该移动的位置\n\t\t\t\tlet lineOffsetLeft = this.list\n\t\t\t\t\t.slice(0, this.innerCurrent)\n\t\t\t\t\t.reduce((total, curr) => total + curr.rect.width, 0);\n                // 获取下划线的数值px表示法\n\t\t\t\tconst lineWidth = uni.$u.getPx(this.lineWidth);\n\t\t\t\tthis.lineOffsetLeft = lineOffsetLeft + (tabItem.rect.width - lineWidth) / 2\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 第一次移动滑块，无需过渡时间\n\t\t\t\tthis.animation(this.lineOffsetLeft, this.firstTime ? 0 : parseInt(this.duration))\n\t\t\t\t// #endif\n\n\t\t\t\t// 如果是第一次执行此方法，让滑块在初始化时，瞬间滑动到第一个tab item的中间\n\t\t\t\t// 这里需要一个定时器，因为在非nvue下，是直接通过style绑定过渡时间，需要等其过渡完成后，再设置为false(非第一次移动滑块)\n\t\t\t\tif (this.firstTime) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.firstTime = false\n\t\t\t\t\t}, 10);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// nvue下设置滑块的位置\n\t\t\tanimation(x, duration = 0) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tconst ref = this.$refs['u-tabs__wrapper__nav__line']\n\t\t\t\tanimation.transition(ref, {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\ttransform: `translateX(${x}px)`\n\t\t\t\t\t},\n\t\t\t\t\tduration\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 点击某一个标签\n\t\t\tclickHandler(item, index) {\n\t\t\t\t// 因为标签可能为disabled状态，所以click是一定会发出的，但是change事件是需要可用的状态才发出\n\t\t\t\tthis.$emit('click', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t})\n\t\t\t\t// 如果disabled状态，返回\n\t\t\t\tif (item.disabled) return\n\t\t\t\tthis.innerCurrent = index\n\t\t\t\tthis.resize()\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 长按事件\n\t\t\tlongPressHandler(item, index) {\n\t\t\t\tthis.$emit('longPress', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t})\n\t\t\t},\n\t\t\tinit() {\n\t\t\t\tuni.$u.sleep().then(() => {\n\t\t\t\t\tthis.resize()\n\t\t\t\t})\n\t\t\t},\n\t\t\tsetScrollLeft() {\n\t\t\t\t// 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\n\t\t\t\tconst tabRect = this.list[this.innerCurrent]\n\t\t\t\t// 累加得到当前item到左边的距离\n\t\t\t\tconst offsetLeft = this.list\n\t\t\t\t\t.slice(0, this.innerCurrent)\n\t\t\t\t\t.reduce((total, curr) => {\n\t\t\t\t\t\treturn total + curr.rect.width\n\t\t\t\t\t}, 0)\n\t\t\t\t// 此处为屏幕宽度\n\t\t\t\tconst windowWidth = uni.$u.sys().windowWidth\n\t\t\t\t// 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\n\t\t\t\tlet scrollLeft = offsetLeft - (this.tabsRect.width - tabRect.rect.width) / 2 - (windowWidth - this.tabsRect\n\t\t\t\t\t.right) / 2 + this.tabsRect.left / 2\n\t\t\t\t// 这里做一个限制，限制scrollLeft的最大值为整个scroll-view宽度减去tabs组件的宽度\n\t\t\t\tscrollLeft = Math.min(scrollLeft, this.scrollViewWidth - this.tabsRect.width)\n\t\t\t\tthis.scrollLeft = Math.max(0, scrollLeft)\n\t\t\t},\n\t\t\t// 获取所有标签的尺寸\n\t\t\tresize() {\n\t\t\t\t// 如果不存在list，则不处理\n\t\t\t\tif(this.list.length === 0) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tPromise.all([this.getTabsRect(), this.getAllItemRect()]).then(([tabsRect, itemRect = []]) => {\n\t\t\t\t\tthis.tabsRect = tabsRect\n\t\t\t\t\tthis.scrollViewWidth = 0\n\t\t\t\t\titemRect.map((item, index) => {\n\t\t\t\t\t\t// 计算scroll-view的宽度，这里\n\t\t\t\t\t\tthis.scrollViewWidth += item.width\n\t\t\t\t\t\t// 另外计算每一个item的中心点X轴坐标\n\t\t\t\t\t\tthis.list[index].rect = item\n\t\t\t\t\t})\n\t\t\t\t\t// 获取了tabs的尺寸之后，设置滑块的位置\n\t\t\t\t\tthis.setLineLeft()\n\t\t\t\t\tthis.setScrollLeft()\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取导航菜单的尺寸\n\t\t\tgetTabsRect() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tthis.queryRect('u-tabs__wrapper__scroll-view').then(size => resolve(size))\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取所有标签的尺寸\n\t\t\tgetAllItemRect() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tconst promiseAllArr = this.list.map((item, index) => this.queryRect(\n\t\t\t\t\t\t`u-tabs__wrapper__nav__item-${index}`, true))\n\t\t\t\t\tPromise.all(promiseAllArr).then(sizes => resolve(sizes))\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取各个标签的尺寸\n\t\t\tqueryRect(el, item) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t// $uGetRect为uView自带的节点查询简化方法，详见文档介绍：https://www.uviewui.com/js/getRect.html\n\t\t\t\t// 组件内部一般用this.$uGetRect，对外的为uni.$u.getRect，二者功能一致，名称不同\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tthis.$uGetRect(`.${el}`).then(size => {\n\t\t\t\t\t\tresolve(size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下，使用dom模块查询元素高度\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(item ? this.$refs[el][0] : this.$refs[el], res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-tabs {\n\n\t\t&__wrapper {\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&__scroll-view-wrapper {\n\t\t\t\tflex: 1;\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\toverflow: auto hidden;\n\t\t\t\t/* #endif */\n\t\t\t}\n\n\t\t\t&__scroll-view {\n\t\t\t\t@include flex;\n\t\t\t\tflex: 1;\n\t\t\t}\n\n\t\t\t&__nav {\n\t\t\t\t@include flex;\n\t\t\t\tposition: relative;\n\n\t\t\t\t&__item {\n\t\t\t\t\tpadding: 0 11px;\n\t\t\t\t\t@include flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t}\n\n\t\t\t\t\t&__text {\n\t\t\t\t\t\tfont-size: 15px;\n\t\t\t\t\t\tcolor: $u-content-color;\n\n\t\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t\tcolor: $u-disabled-color !important;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&__line {\n\t\t\t\t\theight: 3px;\n\t\t\t\t\tbackground: $u-primary;\n\t\t\t\t\twidth: 30px;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbottom: 2px;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\ttransition-property: transform;\n\t\t\t\t\ttransition-duration: 300ms;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=0de61367&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=0de61367&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753626642022\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}