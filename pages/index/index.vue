<template>
	<view class="page">
		<!-- 固定搜索栏 -->

			<view class="search-bar-fixed" :class="{ 'search-bar-sticky': isSearchSticky }">
				<view class="search-input">
					<u-search placeholder="焊工" v-model="searchKeyword" :show-action="false" bg-color="#f5f5f5"
						shape="round" @search="onSearch" />
				</view>
				<view class="search-actions">
					<u-button type="primary" size="small"
						:custom-style="{ backgroundColor: '#4A90E2', fontSize: '28rpx' }" @click="onSearch">
						搜索
					</u-button>
				</view>
			</view>
		
			<scroll-view>
				<view class="search-bar-fixed">
<!-- 推荐职位 Tabs -->
<view class="recommend-section">
	<view>
		<u-tabs :list="recommendTabs" :current="currentRecommendTab" @change="handleRecommendTabChange"
			:active-color="'#093C6B'" :inactive-color="'#666'" font-size="28" :bar-width="60" bar-height="4">
		</u-tabs>
	</view>
	<view class="recommend-content">
		<view class="recommend-tags">
			<u-icon name="plus" size="14" color="#093C6B" @click="handleAddTag" />
		</view>
	</view>
</view>

<!-- 功能图标 -->
<view class="func-bar">
	<view class="func-item" v-for="item in funcItems" :key="item.name" @click="handleFuncClick(item)">
		<view class="func-icon">
			<u-icon :name="item.icon" size="28" :color="item.color" />
		</view>
		<text class="func-text">{{ item.name }}</text>
	</view>
</view>
<u-sticky>
<!-- 排序筛选栏 -->
<view class="sort-filter-bar">
	<view class="sort-section">
		<view class="sort-item" v-for="sort in sortOptions" :key="sort.key"
			:class="{ 'active': currentSort === sort.key }" @click="handleSortChange(sort.key)">
			<text class="sort-text">{{ sort.label }}</text>
			<u-icon v-if="sort.sortable" :name="getSortIcon(sort.key)" size="12"
				:color="currentSort === sort.key ? '#093C6B' : '#999'" />
		</view>
	</view>
	<view class="filter-section">
		<view class="filter-item" @click="handleLocationFilter">
			<text class="filter-text">{{ currentLocation }}</text>
			<u-icon name="arrow-down" size="12" color="#666" />
		</view>
		<view class="filter-item" @click="handleAdvancedFilter">
			<u-icon name="list" size="16" color="#666" />
			<text class="filter-text">筛选</text>
		</view>
	</view>
</view>
</u-sticky>

<!-- 职位列表 -->
<view class="job-list">
	<view class="job-card" v-for="job in jobList" :key="job.id">
		<view class="job-header">
			<view class="job-info">
				<text class="job-title">{{ job.title }}</text>
				<text class="job-salary">{{ job.salary }}</text>
				<text class="job-category">{{ job.category }}</text>
				<text class="job-location">{{ job.location }}</text>
			</view>
			<u-icon name="close" size="16" color="#ccc" @click="handleCloseJob(job)" />
		</view>

		<view class="job-footer">
			<view class="recruiter-info">
				<u-avatar :src="job.recruiter.avatar" size="40" />
				<view class="recruiter-details">
					<text class="recruiter-name">{{ job.recruiter.name }}</text>
					<text class="recruiter-company">{{ job.recruiter.company }}</text>
					<text class="recruiter-activity">{{ job.recruiter.activity }}</text>
				</view>
			</view>
			<u-button :type="job.buttonText === '免费聊' ? 'primary' : 'info'" size="small" :custom-style="{
				backgroundColor: job.buttonText === '免费聊' ? '#093C6B' : '#4A90E2',
				fontSize: '26rpx',
				padding: '12rpx 24rpx'
			}" @click="handleCommunicate(job)">
				{{ job.buttonText }}
			</u-button>
		</view>
	</view>
</view>
				</view>
			</scroll-view>

		<!-- 底部浮动按钮 -->
		<view class="float-buttons">
			<u-button type="primary" size="default" :custom-style="{
				backgroundColor: '#093C6B',
				marginRight: '20rpx',
				borderRadius: '50rpx',
				fontSize: '28rpx'
			}" @click="handlePublishJob">
				<u-icon name="plus" color="white" size="16" />
				免费发布职位
			</u-button>
			<u-button type="info" plain size="default" :custom-style="{
				borderRadius: '50rpx',
				fontSize: '28rpx'
			}" @click="handleManageResume">
				管理我的简历
			</u-button>
		</view>

		<!-- 公共TabBar -->
		<view></view>
	</view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue'

export default {
	components: { CustomTabbar },
	data() {
		return {
			searchKeyword: '',
			isSearchSticky: false,
			// 推荐职位 Tabs
			recommendTabs: [
				{ name: '推荐职位' },
				{ name: '前端开发工程师' },
				{ name: 'PHP' },
				{ name: '运营专员' },
				{ name: '测试工程师' },
			],
			currentRecommendTab: 0,
			funcItems: [
				{ name: '免费进群', icon: 'account-fill', color: '#093C6B' },
				{ name: '名企招聘', icon: 'bookmark-fill', color: '#093C6B' },
				{ name: '人事行政', icon: 'file-text-fill', color: '#093C6B' },
				{ name: '客服运营', icon: 'server-man', color: '#093C6B' },
				{ name: '兼职专区', icon: 'clock-fill', color: '#093C6B' }
			],
			// 排序选项
			sortOptions: [
				{ key: 'comprehensive', label: '综合', sortable: false },
				{ key: 'latest', label: '最新', sortable: true },
				{ key: 'nearby', label: '附近', sortable: false }
			],
			currentSort: 'comprehensive',
			sortDirection: 'desc', // 'asc' 或 'desc'
			currentLocation: '深圳',
			jobList: [
				{
					id: 1,
					title: '前端开发工程师',
					salary: '7000-12000元/月',
					category: '前端开发工程师',
					location: '宝安区',
					recruiter: {
						name: '杨先生',
						company: '深圳市恒鼎尚科技有限公司',
						activity: '今日回复6次',
						avatar: '/static/avatar1.png'
					},
					buttonText: '继续聊'
				},
				{
					id: 2,
					title: 'web前端开发工程师',
					salary: '1-2万元/月',
					category: '前端开发工程师',
					location: '南山区',
					recruiter: {
						name: '杨先生',
						company: '胜道和科技有限公司',
						activity: '今日回复7次',
						avatar: '/static/avatar2.png'
					},
					buttonText: '继续聊'
				},
				{
					id: 3,
					title: 'Java后端开发',
					salary: '10-20万元/月',
					category: 'Java',
					location: '龙岗区',
					recruiter: {
						name: '张先生',
						company: '星童云',
						activity: '今日回复2次',
						avatar: '/static/avatar3.png'
					},
					buttonText: '免费聊'
				}
			]
		}
	},
	computed: {
		currentRecommendTags() {
			return this.recommendTagsData[this.currentRecommendTab] || []
		}
	},
	mounted() {
		// 监听页面滚动，实现搜索栏吸顶
		uni.onPageScroll((e) => {
			this.isSearchSticky = e.scrollTop > 100
		})
	},
	methods: {
		onSearch() {
			console.log('搜索:', this.searchKeyword)
		},
		handleRecommendTabChange(index) {
			this.currentRecommendTab = index
			console.log('切换推荐标签:', this.recommendTabs[index].name)
		},
		handleAddTag() {
			console.log('添加标签')
			// 这里可以打开标签选择弹窗，让用户选择更多职位标签
		},
		handleFuncClick(item) {
			console.log('功能点击:', item.name)
		},
		handleSortChange(sortKey) {
			if (this.currentSort === sortKey && this.sortOptions.find(s => s.key === sortKey)?.sortable) {
				// 如果点击的是当前排序且可排序，则切换排序方向
				this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc'
			} else {
				this.currentSort = sortKey
				this.sortDirection = 'desc'
			}
			console.log('排序变更:', sortKey, this.sortDirection)
		},
		getSortIcon(sortKey) {
			if (!this.sortOptions.find(s => s.key === sortKey)?.sortable) return ''
			if (this.currentSort !== sortKey) return 'arrow-down'
			return this.sortDirection === 'asc' ? 'arrow-up' : 'arrow-down'
		},
		handleLocationFilter() {
			console.log('位置筛选')
			// 这里可以打开位置选择弹窗
		},
		handleAdvancedFilter() {
			console.log('高级筛选')
			// 这里可以打开筛选弹窗
		},
		handleCommunicate(job) {
			console.log('沟通职位:', job.title)
		},
		handleCloseJob(job) {
			console.log('关闭职位:', job.title)
		},
		handlePublishJob() {
			console.log('发布职位')
		},
		handleManageResume() {
			console.log('管理简历')
		}
	}
}
</script>

<style scoped>
.page {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 140rpx;
}

.search-bar-fixed {
	background: #fff;
	padding: 24rpx 32rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	gap: 20rpx;
	position: relative;
	z-index: 999;
	transition: all 0.3s ease;
}

.search-bar-sticky {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.search-input {
	flex: 1;
}

.search-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.recommend-section {
	background: #fff;
	margin-top: 16rpx;
	display: flex;
	align-items: center;
}

.recommend-content {
	padding: 24rpx 32rpx;
}

.recommend-tags {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex-wrap: wrap;
}

.func-bar {
	background: #fff;
	padding: 40rpx 32rpx;
	margin-top: 16rpx;
	display: flex;
	justify-content: space-around;
}

.func-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.func-icon {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.func-text {
	font-size: 24rpx;
	color: #666;
}

.sort-filter-bar {
	background: #fff;
	padding: 24rpx 32rpx;
	margin-top: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.sort-section {
	display: flex;
	align-items: center;
	gap: 40rpx;
}

.sort-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 0;
	cursor: pointer;
	transition: all 0.3s ease;
}

.sort-item.active .sort-text {
	color: #093C6B;
	font-weight: 600;
}

.sort-text {
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s ease;
}

.filter-section {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.filter-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.filter-item:active {
	background: #e9ecef;
}

.filter-text {
	font-size: 26rpx;
	color: #666;
}

.job-list {
	padding: 16rpx 32rpx;
}

.job-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.job-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 32rpx;
}

.job-info {
	flex: 1;
}

.job-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.job-salary {
	font-size: 30rpx;
	color: #093C6B;
	font-weight: 600;
	margin-bottom: 16rpx;
	display: block;
}

.job-category {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.job-location {
	font-size: 28rpx;
	color: #666;
	display: block;
}

.job-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.recruiter-info {
	display: flex;
	align-items: center;
	flex: 1;
	gap: 20rpx;
}

.recruiter-details {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.recruiter-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.recruiter-company {
	font-size: 24rpx;
	color: #666;
	max-width: 300rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.recruiter-activity {
	font-size: 22rpx;
	color: #999;
}

.float-buttons {
	position: fixed;
	bottom: 140rpx;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	z-index: 100;
	padding: 0 32rpx;
}
</style>